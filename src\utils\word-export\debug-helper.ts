/**
 * 调试辅助工具
 * 用于保存中间处理结果，便于调试和验证
 */
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * 生成唯一的文件标识符
 * @param prefix 文件前缀
 * @param extension 文件扩展名
 * @returns 唯一文件名
 */
export function generateUniqueFileName(prefix: string, extension: string): string {
  const timestamp = Date.now();
  const uuid = uuidv4().substring(0, 8); // 使用UUID的前8位
  return `${prefix}_${timestamp}_${uuid}.${extension}`;
}

/**
 * 保存预处理后的HTML到文件
 * @param htmlContent 预处理后的HTML内容
 * @param outputPath 输出文件路径（可选）
 * @param sessionId 会话ID（可选，用于区分不同用户）
 */
export function saveProcessedHtml(
  htmlContent: string,
  outputPath?: string,
  sessionId?: string
): string {
  try {
    let filePath: string;

    if (outputPath) {
      filePath = outputPath;
    } else {
      // 生成唯一文件名，避免并发冲突
      const uniqueFileName = sessionId
        ? `processed-html-preview_${sessionId}_${Date.now()}.html`
        : generateUniqueFileName('processed-html-preview', 'html');

      filePath = path.join(
        process.cwd(),
        'temp',
        'debug-files',
        uniqueFileName
      );
    }

    // 确保目录存在
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // 保存文件
    fs.writeFileSync(filePath, htmlContent, 'utf-8');

    console.log(`预处理后的HTML已保存到: ${filePath}`);
    return filePath;
  } catch (error) {
    console.error('保存预处理HTML失败:', error);
    throw error;
  }
}

/**
 * 输出转换过程的关键信息
 * @param stage 转换阶段
 * @param info 信息内容
 */
export function logConversionInfo(stage: string, info: any): void {
  console.log(`[${stage}] ${JSON.stringify(info, null, 2)}`);
}
