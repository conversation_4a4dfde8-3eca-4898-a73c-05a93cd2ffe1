import { Provide } from '@midwayjs/core';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * 文件管理服务
 * 提供并发安全的文件操作功能，避免多用户同时调用时的文件冲突
 */
@Provide()
export class FileManagerService {
  private readonly TEMP_DIR = path.join(process.cwd(), 'temp');
  private readonly WORD_EXPORTS_DIR = path.join(this.TEMP_DIR, 'word-exports');
  private readonly DEBUG_FILES_DIR = path.join(this.TEMP_DIR, 'debug-files');
  private readonly PARSE_RESULTS_DIR = path.join(this.TEMP_DIR, 'parse-results');

  constructor() {
    this.ensureDirectoriesExist();
  }

  /**
   * 确保所有必要的目录存在
   */
  private ensureDirectoriesExist(): void {
    const directories = [
      this.TEMP_DIR,
      this.WORD_EXPORTS_DIR,
      this.DEBUG_FILES_DIR,
      this.PARSE_RESULTS_DIR,
    ];

    directories.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`创建目录: ${dir}`);
      }
    });
  }

  /**
   * 生成唯一的文件名
   * @param baseName 基础文件名
   * @param extension 文件扩展名（不包含点）
   * @param sessionId 会话ID（可选）
   * @returns 唯一文件名
   */
  generateUniqueFileName(baseName: string, extension: string, sessionId?: string): string {
    const timestamp = Date.now();
    const uuid = uuidv4().substring(0, 8);
    const sessionPrefix = sessionId ? `${sessionId}_` : '';
    return `${sessionPrefix}${baseName}_${timestamp}_${uuid}.${extension}`;
  }

  /**
   * 生成会话ID
   * @returns 8位会话ID
   */
  generateSessionId(): string {
    return uuidv4().substring(0, 8);
  }

  /**
   * 获取Word导出文件的唯一路径
   * @param baseName 基础文件名
   * @param sessionId 会话ID（可选）
   * @returns 完整文件路径
   */
  getUniqueWordExportPath(baseName: string, sessionId?: string): string {
    const uniqueFileName = this.generateUniqueFileName(baseName, 'docx', sessionId);
    return path.join(this.WORD_EXPORTS_DIR, uniqueFileName);
  }

  /**
   * 获取调试文件的唯一路径
   * @param baseName 基础文件名
   * @param extension 文件扩展名（不包含点）
   * @param sessionId 会话ID（可选）
   * @returns 完整文件路径
   */
  getUniqueDebugFilePath(baseName: string, extension: string, sessionId?: string): string {
    const uniqueFileName = this.generateUniqueFileName(baseName, extension, sessionId);
    return path.join(this.DEBUG_FILES_DIR, uniqueFileName);
  }

  /**
   * 获取解析结果文件的唯一路径
   * @param baseName 基础文件名
   * @param extension 文件扩展名（不包含点）
   * @param sessionId 会话ID（可选）
   * @returns 完整文件路径
   */
  getUniqueParseResultPath(baseName: string, extension: string, sessionId?: string): string {
    const uniqueFileName = this.generateUniqueFileName(baseName, extension, sessionId);
    return path.join(this.PARSE_RESULTS_DIR, uniqueFileName);
  }

  /**
   * 安全地写入文件
   * @param filePath 文件路径
   * @param content 文件内容
   * @param encoding 编码格式（默认utf-8）
   */
  async writeFileSync(filePath: string, content: string | Buffer, encoding: BufferEncoding = 'utf-8'): Promise<void> {
    try {
      // 确保目录存在
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // 写入文件
      if (typeof content === 'string') {
        await fs.promises.writeFile(filePath, content, encoding);
      } else {
        await fs.promises.writeFile(filePath, content);
      }

      console.log(`文件已保存: ${filePath}`);
    } catch (error) {
      console.error(`写入文件失败: ${filePath}`, error);
      throw error;
    }
  }

  /**
   * 安全地读取文件
   * @param filePath 文件路径
   * @param encoding 编码格式（默认utf-8）
   * @returns 文件内容
   */
  async readFile(filePath: string, encoding: BufferEncoding = 'utf-8'): Promise<string> {
    try {
      return await fs.promises.readFile(filePath, encoding);
    } catch (error) {
      console.error(`读取文件失败: ${filePath}`, error);
      throw error;
    }
  }

  /**
   * 检查文件是否存在
   * @param filePath 文件路径
   * @returns 是否存在
   */
  fileExists(filePath: string): boolean {
    return fs.existsSync(filePath);
  }

  /**
   * 删除文件
   * @param filePath 文件路径
   */
  async deleteFile(filePath: string): Promise<void> {
    try {
      if (this.fileExists(filePath)) {
        await fs.promises.unlink(filePath);
        console.log(`文件已删除: ${filePath}`);
      }
    } catch (error) {
      console.error(`删除文件失败: ${filePath}`, error);
      throw error;
    }
  }

  /**
   * 获取临时目录路径
   */
  getTempDir(): string {
    return this.TEMP_DIR;
  }

  /**
   * 获取Word导出目录路径
   */
  getWordExportsDir(): string {
    return this.WORD_EXPORTS_DIR;
  }

  /**
   * 获取调试文件目录路径
   */
  getDebugFilesDir(): string {
    return this.DEBUG_FILES_DIR;
  }

  /**
   * 获取解析结果目录路径
   */
  getParseResultsDir(): string {
    return this.PARSE_RESULTS_DIR;
  }

  /**
   * 清理过期文件
   * @param directory 目录路径
   * @param maxAge 最大文件年龄（毫秒）
   */
  async cleanupExpiredFiles(directory: string, maxAge: number = 60 * 60 * 1000): Promise<void> {
    try {
      if (!fs.existsSync(directory)) {
        return;
      }

      const files = await fs.promises.readdir(directory);
      const now = Date.now();

      for (const file of files) {
        const filePath = path.join(directory, file);
        const stats = await fs.promises.stat(filePath);
        const fileAge = now - stats.mtimeMs;

        if (fileAge > maxAge) {
          await this.deleteFile(filePath);
        }
      }
    } catch (error) {
      console.error(`清理过期文件失败: ${directory}`, error);
    }
  }

  /**
   * 清理所有临时目录的过期文件
   * @param maxAge 最大文件年龄（毫秒，默认1小时）
   */
  async cleanupAllExpiredFiles(maxAge: number = 60 * 60 * 1000): Promise<void> {
    const directories = [
      this.WORD_EXPORTS_DIR,
      this.DEBUG_FILES_DIR,
      this.PARSE_RESULTS_DIR,
    ];

    await Promise.all(
      directories.map(dir => this.cleanupExpiredFiles(dir, maxAge))
    );
  }
}
